import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

/// A modern card widget that displays invoice summary information
class InvoiceCard extends StatelessWidget {
  final Invoice invoice;
  final Client? client;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onStatusChange;
  final VoidCallback? onDetail;
  final VoidCallback? onPreviewPdf;

  const InvoiceCard({
    super.key,
    required this.invoice,
    this.client,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onStatusChange,
    this.onDetail,
    this.onPreviewPdf,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.04),
            blurRadius: 40,
            offset: const Offset(0, 16),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  theme.colorScheme.surface,
                  theme.colorScheme.surface.withValues(alpha: 0.95),
                ],
              ),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with invoice number and status
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Invoice',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0.5,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              invoice.invoiceNumber,
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                letterSpacing: -0.5,
                              ),
                            ),
                          ],
                        ),
                      ),
                      _buildModernStatusChip(context),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Client section with avatar
                  if (client != null) ...[
                    Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                theme.colorScheme.primary,
                                theme.colorScheme.primary.withValues(alpha: 0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Center(
                            child: Text(
                              client!.name.isNotEmpty ? client!.name[0].toUpperCase() : 'C',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                client!.name,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              if (client!.email != null) ...[
                                const SizedBox(height: 2),
                                Text(
                                  client!.email!,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                  ],

                  // Invoice details in modern grid
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.outline.withValues(alpha: 0.1),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: _buildDetailItem(
                            context,
                            'Issue Date',
                            LocaleDateUtils.formatDate(invoice.issueDate),
                            Icons.calendar_today,
                          ),
                        ),
                        if (invoice.dueDate != null) ...[
                          Container(
                            width: 1,
                            height: 40,
                            color: theme.colorScheme.outline.withValues(alpha: 0.2),
                          ),
                          Expanded(
                            child: _buildDetailItem(
                              context,
                              'Due Date',
                              LocaleDateUtils.formatDate(invoice.dueDate!),
                              Icons.schedule,
                              isOverdue: invoice.isOverdue,
                              alignment: CrossAxisAlignment.end,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Total amount with modern styling
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          theme.colorScheme.primaryContainer,
                          theme.colorScheme.primaryContainer.withValues(alpha: 0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.primary.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.account_balance_wallet,
                          color: theme.colorScheme.onPrimaryContainer,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Total Amount',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                CurrencyService.formatCurrency(
                                  invoice.total,
                                  invoice.currency,
                                  InvoiceLocalizationService.getLocaleFromLanguage(invoice.locale),
                                ),
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onPrimaryContainer,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Overdue warning with modern design
                  if (invoice.isOverdue) ...[
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            theme.colorScheme.errorContainer,
                            theme.colorScheme.errorContainer.withValues(alpha: 0.8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.colorScheme.error.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.warning_amber_rounded,
                            size: 18,
                            color: theme.colorScheme.onErrorContainer,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${invoice.daysPastDue} days overdue',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onErrorContainer,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],

                  // Modern action buttons
                  if (onEdit != null || onDelete != null || onStatusChange != null || onDetail != null || onPreviewPdf != null) ...[
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        if (onDetail != null)
                          Expanded(
                            child: _buildModernActionButton(
                              context,
                              'Details',
                              Icons.info_outline_rounded,
                              onDetail!,
                              isPrimary: true,
                            ),
                          ),
                        if (onDetail != null && (onPreviewPdf != null || onStatusChange != null || onEdit != null || onDelete != null))
                          const SizedBox(width: 6),
                        if (onPreviewPdf != null)
                          Expanded(
                            child: _buildModernActionButton(
                              context,
                              'Preview',
                              Icons.visibility_rounded,
                              onPreviewPdf!,
                              isPrimary: true,
                            ),
                          ),
                        if (onPreviewPdf != null && (onStatusChange != null || onEdit != null || onDelete != null))
                          const SizedBox(width: 6),
                        if (onStatusChange != null)
                          Expanded(
                            child: _buildModernActionButton(
                              context,
                              'Status',
                              Icons.edit_note_rounded,
                              onStatusChange!,
                            ),
                          ),
                        if (onStatusChange != null && (onEdit != null || onDelete != null))
                          const SizedBox(width: 6),
                        if (onEdit != null)
                          Expanded(
                            child: _buildModernActionButton(
                              context,
                              'Edit',
                              Icons.edit_rounded,
                              onEdit!,
                            ),
                          ),
                        if (onEdit != null && onDelete != null)
                          const SizedBox(width: 6),
                        if (onDelete != null)
                          Expanded(
                            child: _buildModernActionButton(
                              context,
                              'Delete',
                              Icons.delete_rounded,
                              onDelete!,
                              isDestructive: true,
                            ),
                          ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    bool isOverdue = false,
    CrossAxisAlignment alignment = CrossAxisAlignment.start,
  }) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: alignment,
      children: [
        Row(
          mainAxisAlignment: alignment == CrossAxisAlignment.start
              ? MainAxisAlignment.start
              : MainAxisAlignment.end,
          children: [
            if (alignment == CrossAxisAlignment.end) ...[
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 6),
            ],
            Icon(
              icon,
              size: 16,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            if (alignment == CrossAxisAlignment.start) ...[
              const SizedBox(width: 6),
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isOverdue
                ? theme.colorScheme.error
                : theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildModernActionButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onPressed, {
    bool isPrimary = false,
    bool isDestructive = false,
  }) {
    final theme = Theme.of(context);

    return Container(
      height: 40,
      decoration: BoxDecoration(
        gradient: isPrimary
            ? LinearGradient(
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.primary.withValues(alpha: 0.8),
                ],
              )
            : isDestructive
                ? LinearGradient(
                    colors: [
                      theme.colorScheme.error,
                      theme.colorScheme.error.withValues(alpha: 0.8),
                    ],
                  )
                : null,
        color: isPrimary || isDestructive
            ? null
            : theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isPrimary
              ? theme.colorScheme.primary.withValues(alpha: 0.3)
              : isDestructive
                  ? theme.colorScheme.error.withValues(alpha: 0.3)
                  : theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: isPrimary
                      ? theme.colorScheme.onPrimary
                      : isDestructive
                          ? theme.colorScheme.onError
                          : theme.colorScheme.onSurface,
                ),
                const SizedBox(width: 6),
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isPrimary
                        ? theme.colorScheme.onPrimary
                        : isDestructive
                            ? theme.colorScheme.onError
                            : theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernStatusChip(BuildContext context) {
    final theme = Theme.of(context);

    Color backgroundColor;
    Color textColor;
    IconData icon;
    Color borderColor;

    switch (invoice.status) {
      case InvoiceStatus.draft:
        backgroundColor = theme.colorScheme.surfaceContainerHighest;
        textColor = theme.colorScheme.onSurface;
        icon = Icons.edit_note_rounded;
        borderColor = theme.colorScheme.outline.withValues(alpha: 0.3);
        break;
      case InvoiceStatus.sent:
        backgroundColor = theme.colorScheme.primaryContainer;
        textColor = theme.colorScheme.onPrimaryContainer;
        icon = Icons.send_rounded;
        borderColor = theme.colorScheme.primary.withValues(alpha: 0.3);
        break;
      case InvoiceStatus.paid:
        backgroundColor = theme.colorScheme.tertiaryContainer;
        textColor = theme.colorScheme.onTertiaryContainer;
        icon = Icons.check_circle_rounded;
        borderColor = theme.colorScheme.tertiary.withValues(alpha: 0.3);
        break;
      case InvoiceStatus.overdue:
        backgroundColor = theme.colorScheme.errorContainer;
        textColor = theme.colorScheme.onErrorContainer;
        icon = Icons.warning_rounded;
        borderColor = theme.colorScheme.error.withValues(alpha: 0.3);
        break;
      case InvoiceStatus.cancelled:
        backgroundColor = theme.colorScheme.surfaceContainerHighest;
        textColor = theme.colorScheme.onSurface.withValues(alpha: 0.6);
        icon = Icons.cancel_rounded;
        borderColor = theme.colorScheme.outline.withValues(alpha: 0.3);
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: borderColor,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: textColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: textColor,
          ),
          const SizedBox(width: 6),
          Text(
            _getStatusDisplayText(invoice.status),
            style: theme.textTheme.bodySmall?.copyWith(
              color: textColor,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.3,
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusDisplayText(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return 'Draft';
      case InvoiceStatus.sent:
        return 'Sent';
      case InvoiceStatus.paid:
        return 'Paid';
      case InvoiceStatus.overdue:
        return 'Overdue';
      case InvoiceStatus.cancelled:
        return 'Cancelled';
    }
  }
}